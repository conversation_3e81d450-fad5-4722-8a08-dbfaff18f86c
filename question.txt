Maze to Savor!
Maze of UCSC is classical maze runner game where the end goal is to capture the flag randomly placed on 
the maze. The maze has three floors. Each floor is made out of standing blocks which are square shaped 
and is four (4) square foot in area. The description of floors is as follows:
 Floor 1: - Has a total floor area of 856 square foot with ten (10) blocks width and 25 blocks length. 
There is a starting area in this floor that is four (4) blocks in width and nine (9) blocks in length 
and it is placed along the south border of the floor and its starting position is the eight (8) block 
along the length.
 Floor 2: - Has a floor area of 784 square foot with two rectangular areas with ten (10) blocks width 
and eight (8) blocks length connected by a bridge that is of four (4) blocks width and nine (9) 
blocks length situated right above the standing area.
 Floor 3: - Has floor area of 360 square foot and is a rectangular area of ten (10) blocks width and 
nine (9) blocks length situated right above the standing area.
Each block, whether it is game block, standing area, bridge is referred assuming all three floors are 
rectangular in shape with ten (10) blocks width and 25 blocks length and the floor number subject to the 
positional restrictions of the floor design given above. The numbering starts from zero (0) and the blocks 
of each floor is referred to in the following format:
[floor number, cell number on width side, cell number on length side]
 Value ranges are as follows
o Floor number  0, 1, 2
o Cell number on width side  0 to 9 (both included)
o Cell number on length side  0 to 24 (both included)
 For example, first cell on the ground floor is referred as [0, 0, 0].
 The first cell block of the ground floor is the top left hand corner of the floor and all 
other floors are referred using the locations are referred with respect to the ground 
floor and only the floor numbers differ.
There are stairs and poles within the maze that controls the movement of the players. The rules regarding 
the stairs and poles are as follows:
Stairs
The stairs have a starting cell block and an ending cell block and is given in the following format.
[start floor, start block width number, start block length number, end floor, end block width number, end 
block length number]
 Stairs are always defined in the form from a lower floor to an upper floor in the maze.
 If a player lands on the start cell or the end cell of the ladder, the player is instantly moved to the 
other end.
 Stairs are unidirectional unless otherwise specified. This means stairs can be used to go both ways.
 Example for a stair case starting at ground floor block 4, 5 and ending at third floor block 0, 10 is 
given using the format [0, 4, 5, 2, 0, 10]
Poles
The poles have only cell block position and a starting and an ending floor. Poles are in the same cell across 
all three floors.
[start floor, end floor, block width number, block length number]
 Poles are one directional and the only way of movement is from a higher floor to a lower floor.
 If the pole is from the third floor to the ground floor, then there is also an entrance through the 
second floor since the pole runs through it.
 A pole that is place from the ground floor to the third floor at block number 5, 24 is given in the 
form [0, 2, 5, 24]. So when a player reaches 5, 24 cell in the first floor or the second floor they will 
be moved to the 5, 24 cell of the ground floor. 
Walls
Walls are placed on the floor of the maze and is represented as impassable obstacles on the floor of the 
maze. The walls are presented as straight line segments and are confined to the size of a single cell block 
and to the floor that is specified. Walls are provided in the following form:
[floor number, starting block width number, starting block length number, ending block width number, 
ending block length number]
 For example, a wall from block cell as 0, 2 to block cell 8, 2 in the first floor is given by [1, 0, 2, 8, 
2].
 No player shall pass through a wall unless under special conditions.
Goal
The goal of the game is to capture the flag place in the location given in the following format:
[floor, block width number, block length number]
Gameplay
 Rule 1: Maze Runner at UCSC is a three player game between players A, B and C and the starting 
locations of each of them in the starting area are given below.
o A -> 6, 12, and first maze Cell to enter is 5, 12 and the current direction is North
o B -> 9, 8 and the first maze Cell to enter is 9, 7 and the current direction is West
o C -> 9, 16 and the first maze Cell to enter is 9, 17 and the current direction is East
 Rule 2: Each player roles two dices to progress in the game. The functionality of the two dices is
given below.
o The two dices are six sided and named the movement dice and the direction dice.
o Movement dice has values from 1 to 6 in the its each side.
o The direction dice have two empty faces and the other four faces have a direction of 
north, east, south, and west. The six faces are as follows:
 Face 1 -> Empty
 Face 2 -> North
 Face 3 -> East
 Face 4 -> South
 Face 5 -> West
 Face 6 -> Empty
o At the beginning of the game the players are placed in their respective location and 
players start by rolling the movement dice. Each player needs to roll a six (6) in the 
movement dice to enter the maze and when a six (6) is rolled they move to the first maze 
cell specified for the relevant player as specified above.
o Once the player has entered the maze the player should be move the number of cells that 
is represented by the number on the movement dice if such movement is possible. The 
direction of the movement is governed by the direction dice. The initial direction is given 
by the Rule 1 above.
 The direction dice is not thrown always. It is thrown with the movement dice at 
each fourth throw. That means after throwing a six (6) in the movement dice the 
player enters the maze and the player will continue to move through the maze in 
their initial directions for the next three throws of the movement dice. For 
example, Player A throws a six (6) and enters the maze at its initial location cell 5, 
12. Then Player A throws two (2), two (2), and one (1) in the next three throws. 
Since its initial direction is ‘North’ after the first throw Player A will be at 3, 12, 
then 1, 12 and finally at 0, 12. 
 On every fourth throw when the direction dice is rolled the players direction is 
now changed to what is indicated on the face of the direction dice. In case the 
face up is an empty face that means the Player has to remain on the same 
direction as previously and if any direction is indicated the player has to change 
its direction to the direction so specified and then move the number of squares 
indicated by the movement dice.
 The player is only to move if and only if the entire number of cells can be moved. 
There would be no partial movement in case of coming to the edge of the maze 
or an obstacle. If a player’s movement direction is towards and obstacle the 
player remains in position until its direction changes so movement is possible.
 If the player meets pole or stair cells when moving and does not land on such, 
then such poles or stairs have no effect on the movement of the player and stair 
movement or the pole movement is not activated.
o A single round in the game is where all three players have finished rolling the dices.
 Rule 3: The Goal is to reach and capture the flag. Once a player captures the flag, the game ends.
If the game is developed as specified without the additional gameplay elements given below a student 
will obtain 40% of the mark allocated for the assignment which is consistent with a pass grade. To obtain 
higher grades, the additional gameplay elements listed below has to implemented.
Additional Gameplay Elements
 Rule 4: Overrides last part of Rule 2 where if a player now comes across a stair cell or a pole cell 
in the middle of the movement the player has to take the pole or the stair movement and perform 
the remaining movement of the movement dice from the landing cell. For example, a player has 
thrown six (6) on the movement dice and now moving from cell 6, 3 North bound to 0, 3. However, 
there is a stair cell on cell 3,3 which takes player to cell 6,7 in the second floor. In this case the 
player will move to cell 3,3 take the stairs to the second floor and then move three more cells to 
cell 3,7 of the second floor.
 Rule 5: If a player lands on cell occupied by another player, the other player is considered captured 
and moved to the starting cell in the starting area. The players are not to capture another player 
if they are to not land on the same cell. In such case it will be considered the player has jumped 
over the current player.
 Rule 6: For a specified number of turns (defined globally as a game setting) the direction of the 
stairs change. The direction of the stairs is unidirectional, one way up, one way down. The changes 
are at random and changed every five rounds. 
 Rule 7: Bawana is a square shaped area in the ground floor of the maze located in the following 
boundary:
o From 6, 20 to 9, 20 and is separated from a wall which is of single cell width from 6, 20 to 
9, 20 and from 6, 20 to 6, 24. 
o The entrance is the cell 9, 19 and is a one-way entrance where players can go out of the 
area but cannot walk into the area by movement. If a player is placed in the entrance cell 
after visiting Bawana they will always be placed there with a North movement direction.
o There are 12 cell blocks within the Bawana area and they are to give following benefits to 
the player when the player lands on one of the 12 cell blocks.
 Food Poisoning: The player is incapacitated and has a stomachache after eating 
and therefore will miss the next three (3) throws. After the expiry of the three 
turns the player is again placed on one of the 12 cells randomly and the effects 
are applied.
 Disoriented: The player gets disoriented after eating at Bawana. The player gets
50 movement points and placed in the entrance cell. However, after moving away 
from the door the disorientation sets in and the player moves on randomly for 
the next four throws. The movement direction is determined by randomly 
selecting one of the four directions and when disoriented, such effects will 
override the direction dices value until the effects have worn off.
 Triggered: After consuming food from Bawana, the player is agitated. He gets 50 
movement points and is placed in the entrance cell. When moving, he moves 
twice as fast so if the movement dice face shows four (4), the player moves eight 
(8) spaces.
 Happy: After consuming food from Bawana, the player is happy and gets 200 
movement points and placed in the entrance cell. There are no other effects on 
the player when happy.
 Bawana shall have two (2) of each type of cells within the 12 cells and then the 
remaining four (4) cells shall give movement points between 10 to 100 at random.
 Rule 8: Each cell in the maze have a consumable value between zero (0) to four (4). When a player 
moves through them the consumable value is deducted from the movement points. For example, 
let’s say a player has 20 movement points and has rolled four in the movement dice. The four 
squares that the player travels and eventually lands have 1, 4, 2, and 1 as the consumable value. 
As such 8 movement points are deducted from the 20 movement points and the player now is 
placed on the final movement cell with 12 movement points remaining.
 Rule 9: Maze can also have cells that given movement bonuses to the player by increasing 
movement points by adding one (1) to five (5) movement points to the overall movement points 
or multiplying them by a factor of two (2) and three (3). 
 Rule 10: The consumable value or bonus value or multiplier is set at random at the initialization 
of the game and will not change through the game. The cell distribution are as follows:
o Consumable zero value cells 25% of all cells
o 35% of cells will have a consumable value between one (1) to four (4).
o 25% of cells will have bonuses of one (1) or two (2).
o 10% of cells will have bonuses of three (3) to five (5).
o 5% of cells will have bonuses of multiply factor of two (2) and three (3).
 Rule 11: If the movement points fall to zero or become negative in the course of movement, the 
player is transported to Bawana immediately and Rule 7 applies to the player.
 Rule 12: Each player has 100 movement points at the beginning. Each die role would cost two 
movement points if the player does not move due to blocking by walls.
Clarification on Rules [as discussed in class]
 For food poisoning, after three (3) rounds have expired, the player still has zero (0) movement 
points. This means that the player will have to be placed in another random square at “Bawana”
and the effects there would be applied to the player.
 There can be maximum of two stairs originating from a given cell. In that case the player has to 
choose the stairs that is closest to the flag using an appropriate distance measure. In case of a tie 
where both distances are the same, then the player can have a random choice which stair to take.
 In case if there are stairs that go through a floor to an upper floor, the program should create a 
blocked cell on the floor where the stairs are to go through and no player on that floor should be 
able to access that floor.
 In case where a player moves through a stairs and pole loop that the player moves through 
infinitely in such cases the player loop has to be detected by the program and stopped and the 
player to be moved to the relevant starting cell of that player in the starting area. However, the 
movement points are not reset in this case as in the case of the beginning of the game and the 
player retains the movement points that the player possess before being moved to the starting 
area.
 If a player falls to the starting area as a result of a pole or a stair, the starting area logic is applied. 
However, the movement points are not reset in this case as in the case of the beginning of the 
game and the player retains the movement points that the player possess before being moved to 
the starting area.
 The height of the building is not given as such, it acceptable to assume a constant height for both 
the floors.
Inputs
 The placement of stairs, poles, walls, and the flag is given in four separate text files named, 
“stairs.txt”, “poles.txt”, “walls.txt”, and “flag.txt”.
 All entries in the files are in the prescribed format above and each entry would be line separated.
That means each stair, pole, and wall will be on its separate line. There will only be a single entry 
in the flag.txt.
 In addition, there will be another file named “seed.txt” which will contain a single number that 
would be the input used to generate the random number sequence.
Expected Output
 When a player roll the dice the following output messages should be produced.
o When at the starting area
 <Player Name> is at the starting area and rolls <value> 
on the movement dice cannot enter the maze.
 <Player Name> is at the starting area and rolls 6 on 
the movement dice and is placed on <relevant cell
number> of the maze.
o When in the maze without the direction dice
 <Player Name> rolls and <value> on the movement dice
and moves <current direction> by <value> cells and is 
now at <relevant cell number>.
o When in the maze with the direction dice
 <Player Name> rolls and <value> on the movement dice
and <direction> on the direction dice, changes direction 
to <current direction> and moves <value> cells and is 
now at <relevant cell number>.
o When a player is blocked by a wall 
 <Player Name> rolls and <value> on the movement dice
and cannot move in the <current direction>. Player 
remains at <current cell number>
o At each stop the movement points and the current direction of the player has to be 
mentioned after display of the above messages in the following format.
 <Player Name> moved <number of cell> that cost <total 
cost> movement points and is left with <new movement 
points> and is moving in the <current direction>.
 At “Bawana” or being transported to “Bawana”
o Player’s movement points are either zero or drops below zero. Both messages have to be 
output.
 <Player Name> movement points are depleted and requires
replenishment. Transporting to Bawana.
 <Player Name> is place on a <cell type> and effects take 
place.
o When Food poisioning
 <Player Name> eats from Bawana and have a bad case of 
food poisoning. Will need three rounds to recover.
 Each turn after food poisoning should have the following outputs
 <Player Name> is still food poisoned and misses 
the turn.
 When three turns are completed.
 <Player Name> is now fit to proceed from the food 
poisoning episode and now placed on a <cell type> 
and the effects take place.
o When disoriented
 <Player Name> eats from Bawana and is disoriented and 
is placed at the entrance of Bawana with 50 movement 
points.
 When moving with disorientation.
 <Player Name> rolls and <value> on the movement 
dice and is disoriented and move in the <direction> 
and moves <value> cells and is placed at the
<relevant cell number>.
 When disorientation effects are over the following message needs to be output
 <Player Name> has recovered from disorientation.
o When triggered
 <Player Name> eats from Bawana and is triggered due to 
bad quality of food. <Player Name> is placed at the 
entrance of Bawana with 50 movement points.
 Moving when triggered
 <Player Name> is triggered and rolls and <value> 
on the movement dice and move in the <direction> 
and moves <2 x value> cells and is placed at the
<relevant cell number>.
o When happy
 <Player Name> eats from Bawana and is happy. <Player 
Name> is placed at the entrance of Bawana with 200 
movement points.
o Other cells
 <Player Name> eats from Bawana and earns <value>
movement points and is placed at the <relevant cell 
number>.
 When a player lands on a stairs landing cell. The following messages are to be output in addition 
to the other messages.
o <Player Name> lands on <cell number> which is a stair cell. 
<Player A> takes the stairs and now placed at <cell number> 
in floor <floor number>.
 When a player lands on a pole landing cell. The following messages are to be output in addition 
to the other messages.
o <Player Name> lands on <cell number> which is a pole cell. 
<Player A> slides down and now placed at <cell number> in 
floor <floor number