#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <limits.h>
#include <stdbool.h>
#include <math.h>

#define ROWS 10
#define COLS 25
#define FLOORS 3
#define CELLS FLOORS*ROWS*COLS
#define STAIRS_CHANGE_EVERY_ROUNDS 5

typedef enum {
    NORMAL,
    START,
    BAWANA,
    DISABLED,
    WALL
} CellType;

typedef enum {
    NORTH,
    EAST,
    SOUTH,
    WEST
} Direction;

typedef enum {
    BIDIRECTION,
    UP,
    DOWN
} StairDir;

typedef enum {
    HAPPY,
    FOOD_P,
    POINTS,
    TRIGGERED,
    DISORIENTED
} Bw_Type;

typedef struct {
    int s_floor, s_row, s_col, e_floor, e_row, e_col;
    StairDir dir;
} Stair;

typedef struct {
    int s_floor, e_floor, row, col;
} Pole;

typedef struct {
    CellType type;
    Stair *stair[2];
    Pole *pole;
    int floor, row, col;
    int stair_num;
    int cost;
    bool mul;
    Bw_Type bw_type;
    int bw_mp;
}Cell;

typedef struct {
    char name;
    int floor, row, col;
    int  s_row, s_col;
    int  init_row, init_col;
    Direction direction;
    int in_board;
    Direction start_direction;
    int left_to_dir_change;
    int skips;
    bool triggered;
    int mp;
    int dis_left;
} Player;

typedef struct {
    int floor, s_row,s_col, e_row, e_col;
} Wall;


// define main globals
Cell BOARD[FLOORS][ROWS][COLS];
Player PLAYERS[3];
Stair STAIRS[CELLS];
Wall WALLS[CELLS];
Pole POLES[CELLS];

int walls_num=0;
int stairs_num=0;
int poles_num = 0;
int FLAG_FLOOR = 1, FLAG_ROW = 5, FLAG_COL = 15;

// Function declarations
Cell *apply_stairs(Player *P, Cell *current_cell);
Cell *apply_poles(Player *P, Cell *current_cell);
void apply_consumables(Player *p, Cell *cell);
void bawana_effect(Player *p);
char* direction_name(Direction dir);
int rand_int(int a, int b);
void handle_stairs_direction(int round);
void randomize_stairs_direction();



void init_floors() {
    for (int f=0; f<FLOORS; f++) {
        for (int r=0; r<ROWS;r++) {
            for (int c=0; c<COLS; c++) {
                BOARD[f][r][c].floor = f;
                BOARD[f][r][c].row = r;
                BOARD[f][r][c].col = c;
                BOARD[f][r][c].type = NORMAL;
                BOARD[f][r][c].stair_num = 0;
                BOARD[f][r][c].stair[0] = NULL;
                BOARD[f][r][c].stair[1] = NULL;
                BOARD[f][r][c].pole = NULL;
                BOARD[f][r][c].cost = 0;
                BOARD[f][r][c].mul = false;
                BOARD[f][r][c].bw_type = POINTS;
                BOARD[f][r][c].bw_mp = 0;

                if (f == 0) {
                    // Starting area
                    if (c >= 8 && c <= 16 && r >= 6 && r <= 9){
                        BOARD[f][r][c].type = START;
                    }
                    // Bawana area
                    if (c >= 21 && c <= 24 && r >= 7 && r <= 9){
                        BOARD[f][r][c].type = BAWANA;
                    }
                    // Bawana walls
                    if ((c==20 && r >= 6) || (r == 6 && c >= 20 )){
                        BOARD[f][r][c].type = WALL;
                    }
                }
                if (f == 1){
                    // Floor 1: two rectangles + bridge
                    bool in_left_rect = (c >= 0 && c <= 7);
                    bool in_right_rect = (c >= 17 && c <= 24);
                    bool in_bridge = (r >= 6 && r <= 9 && c >= 8 && c <= 16);
                    if (!(in_left_rect || in_right_rect || in_bridge)){
                        BOARD[f][r][c].type = DISABLED;
                    }
                }
                if (f == 2){
                    // Floor 2: only middle rectangle
                    if (!(c >= 8 && c <= 16)){
                        BOARD[f][r][c].type = DISABLED;
                    }
                }
            }
        }
    }
}


void load_stairs(){
    FILE *file = fopen("stairs.txt", "r");
    if (!file)
    {
        perror("Error opening stairs.txt");
        exit(1);
    }

    int count = 0;
    int s_floor, s_row, s_col,e_floor, e_row, e_col;
    while (fscanf(file, " [%d , %d , %d , %d , %d, %d] ", &s_floor, &s_row, &s_col, &e_floor, &e_row, &e_col) == 6){
        STAIRS[count].s_floor = s_floor;
        STAIRS[count].s_row = s_row;
        STAIRS[count].s_col = s_col;  // Fixed: was e_col
        STAIRS[count].e_floor = e_floor;
        STAIRS[count].e_row = e_row;
        STAIRS[count].e_col = e_col;
        STAIRS[count].dir = BIDIRECTION;
        stairs_num=++count;
        }
    fclose(file);
}

void load_walls(){
    FILE *file = fopen("walls.txt", "r");
    if (!file)
    {
        perror("Error opening walls.txt");
        exit(1);
    }

    int count = 0;
    int floor, s_row, s_col, e_row, e_col;
    while (fscanf(file, " [%d , %d , %d , %d , %d] ", &floor, &s_row, &s_col, &e_row, &e_col) == 5){
        if (!(s_col == e_col || s_row == e_row)) {
            continue;
        }
        WALLS[walls_num].floor = floor;
        WALLS[walls_num].s_row = s_row;
        WALLS[walls_num].s_col = s_col;  // Fixed: was e_col
        WALLS[walls_num].e_row = e_row;
        WALLS[walls_num].e_col = e_col;
        walls_num++;
        }
    fclose(file);
}
void load_poles(){
    FILE *file = fopen("poles.txt", "r");
    if (!file)
    {
        perror("Error opening poles.txt");
        exit(1);
    }

    int count = 0;
    int s_floor,e_floor, row, col;
    while (fscanf(file, " [%d , %d , %d , %d] ", &s_floor, &e_floor, &row, &col) == 4){  // Fixed: added & for e_floor, row, col
        POLES[poles_num].s_floor = s_floor;
        POLES[poles_num].e_floor = e_floor;
        POLES[poles_num].row = row;
        POLES[poles_num].col = col;
        poles_num++;
        }
    fclose(file);
}


Cell *get_cell(int floor, int row, int col){
    if (row > 9 || col > 24 || col < 0 || row < 0 || floor > FLOORS) return NULL;
    return &BOARD[floor][row][col];
}

int check_cell(Cell *cell){
    if (!cell) return 0;
    if (cell->row > 9 || cell->col > 24 || cell->col < 0 || cell->row < 0) return 0;
    if (cell->type != NORMAL) return 0;  // Fixed: was !cell->type== NORMAL
    return 1;
}

void init_stairs() {
    for (int i = 0; i < stairs_num; i++) {
        Cell *end_cell = get_cell(STAIRS[i].e_floor, STAIRS[i].e_row, STAIRS[i].e_col);
        Cell *start_cell = get_cell(STAIRS[i].s_floor, STAIRS[i].s_row, STAIRS[i].s_col);
        if (!start_cell || !end_cell || !check_cell(start_cell) || !check_cell(end_cell) || start_cell->stair_num>2 || end_cell->stair_num>2) continue;
        start_cell->stair[start_cell->stair_num++]= &STAIRS[i];
        end_cell->stair[end_cell->stair_num++]= &STAIRS[i];
    }
}


void init_poles() {
    for (int i = 0; i < poles_num; i++) {
        Cell *end_cell = get_cell(POLES[i].e_floor, POLES[i].row, POLES[i].col);
        Cell *start_cell = get_cell(POLES[i].s_floor, POLES[i].row, POLES[i].col);
        if (!check_cell(start_cell) || !check_cell(end_cell) || !start_cell || !end_cell) continue;
        start_cell->pole = &POLES[i];
        end_cell->pole= &POLES[i];
        if (POLES[i].e_floor == 2 && POLES[i].s_floor == 0) {
            Cell *mid_cell = get_cell(1,POLES[i].row, POLES[i].col);
            if (!mid_cell || !check_cell(mid_cell)) continue; //todo: add poles to starting area
            mid_cell->pole=&POLES[i];
        }
                // todo: add rest
    }
}


void init_walls() {
    for (int i = 0; i < walls_num; i++) {
        int floor = WALLS[i].floor;
        int s_row = WALLS[i].s_row;
        int s_col = WALLS[i].s_col;
        int e_row = WALLS[i].e_row;
        int e_col = WALLS[i].e_col;
        
        if (s_row == e_row) {
            // horizontal wall
            int start_col = s_col < e_col ? s_col : e_col;
            int end_col = s_col > e_col ? s_col : e_col;
            for (int c = start_col; c <= end_col; c++) {
                Cell *cell = get_cell(floor, s_row, c);
                if (cell) {
                    cell->type = WALL;
                }
            }
        }
        if (s_col == e_col) {
            // vertical wall  
            int start_row = s_row < e_row ? s_row : e_row;
            int end_row = s_row > e_row ? s_row : e_row;
            for (int r = start_row; r <= end_row; r++) {
                Cell *cell = get_cell(floor, r, s_col);
                if (cell) {
                    cell->type = WALL;
                }
            }
        }
    }
}

int attempt_move(Player *P, int die_roll) {
    Cell *start_cell = get_cell(P->floor, P->row, P->col);
    Cell *current_cell = start_cell;
    int path_blocked = 0;
    int wall_block = 0;
    int remaining = die_roll;
    int cells_moved = 0;

    while (remaining > 0) {
        int new_row = P->row;
        int new_col = P->col;

        switch (P->direction){
            case NORTH:
                new_row--;
                break;
            case EAST:
                new_col++;
                break;
            case SOUTH:
                new_row++;
                break;
            case WEST:
                new_col--;
                break;
            default:
                break;
        }

        Cell *next_cell = get_cell(P->floor, new_row, new_col);
        if (!check_cell(next_cell)){
            path_blocked = 1;
            if (next_cell && next_cell->type == WALL){
                wall_block = 1;
            }
            break;
        }

        // Move to the next cell
        P->row = new_row;
        P->col = new_col;
        current_cell = next_cell;
        cells_moved++;

        // Apply poles and stairs
        Cell *new_cell = apply_poles(P, current_cell);
        if (new_cell != current_cell) {
            P->floor = new_cell->floor;
            P->row = new_cell->row;
            P->col = new_cell->col;
            current_cell = new_cell;
        }

        new_cell = apply_stairs(P, current_cell);
        if (new_cell != current_cell) {
            P->floor = new_cell->floor;
            P->row = new_cell->row;
            P->col = new_cell->col;
            current_cell = new_cell;
        }

        // Apply consumables
        apply_consumables(P, current_cell);

        // Check if movement points depleted
        if (P->mp <= 0) {
            printf("%c movement points are depleted and transporting to Bawana.\n", P->name);
            bawana_effect(P);
            return 1;
        }

        remaining--;
    }

    if (path_blocked) {
        // Reset position
        P->floor = start_cell->floor;
        P->row = start_cell->row;
        P->col = start_cell->col;

        if (wall_block) {
            P->mp -= 2;
            printf("%c moved 0 that cost 2 movement points and is left with %d and is moving in the %s.\n",
                   P->name, P->mp, direction_name(P->direction));
            if (P->mp <= 0) {
                printf("%c movement points are depleted and transporting to Bawana.\n", P->name);
                bawana_effect(P);
            }
        }
        return 0;
    }

    // Check for self-loop
    if (current_cell == start_cell && cells_moved > 0) {
        P->direction = P->start_direction;
        P->in_board = 0;
        P->floor = 0;
        P->row = P->init_row;
        P->col = P->init_col;
        printf("Player %c stuck on a loop and sent to start position\n", P->name);
        return 0;
    }

    printf("%c moved %d cells to [%d,%d,%d]\n", P->name, cells_moved, P->floor, P->row, P->col);
    return 1;
}

int man_dis(Cell *cell1, Cell *cell2){
    int dist = abs(cell1->row-cell2->row)+abs(cell1->col-cell2->col)+abs(cell1->floor-cell2->floor);
    return dist*(abs(cell1->floor-cell2->floor)+1);
}

Cell *apply_stairs(Player *P, Cell *current_cell){
    if (!current_cell || current_cell->stair_num <= 0) return current_cell;
    if (!current_cell->stair[0]) return current_cell;

    Cell *nxt_cell = current_cell;

    // Use the first stair for simplicity
    Stair *stair = current_cell->stair[0];
    Cell *stair_start_cell = get_cell(stair->s_floor, stair->s_row, stair->s_col);
    Cell *stair_end_cell = get_cell(stair->e_floor, stair->e_row, stair->e_col);

    if (!stair_start_cell || !stair_end_cell) return current_cell;

    // Check if we're at the start of the stair and can go up
    if ((stair->dir == UP || stair->dir == BIDIRECTION) &&
        current_cell->floor == stair->s_floor &&
        current_cell->row == stair->s_row &&
        current_cell->col == stair->s_col) {
        if (check_cell(stair_end_cell)) {
            nxt_cell = stair_end_cell;
            printf("%c takes stairs from [%d,%d,%d] to [%d,%d,%d]\n",
                   P->name, current_cell->floor, current_cell->row, current_cell->col,
                   nxt_cell->floor, nxt_cell->row, nxt_cell->col);
        }
    }
    // Check if we're at the end of the stair and can go down
    else if ((stair->dir == DOWN || stair->dir == BIDIRECTION) &&
             current_cell->floor == stair->e_floor &&
             current_cell->row == stair->e_row &&
             current_cell->col == stair->e_col) {
        if (check_cell(stair_start_cell)) {
            nxt_cell = stair_start_cell;
            printf("%c takes stairs from [%d,%d,%d] to [%d,%d,%d]\n",
                   P->name, current_cell->floor, current_cell->row, current_cell->col,
                   nxt_cell->floor, nxt_cell->row, nxt_cell->col);
        }
    }

    return nxt_cell;
}
Cell *apply_poles(Player *P, Cell *current_cell){
    if (!current_cell || !current_cell->pole) return current_cell;

    Cell *nxt_cell = current_cell;
    Cell *pole_start_cell = get_cell(current_cell->pole->s_floor, current_cell->pole->row, current_cell->pole->col);
    Cell *pole_end_cell = get_cell(current_cell->pole->e_floor, current_cell->pole->row, current_cell->pole->col);

    if (pole_start_cell && pole_end_cell &&
        current_cell->floor >= pole_start_cell->floor &&
        current_cell->row == pole_end_cell->row &&
        current_cell->col == pole_end_cell->col){
        if (check_cell(pole_start_cell)){
            nxt_cell = pole_start_cell;
            printf("%c slides down pole from [%d,%d,%d] to [%d,%d,%d]\n",
                   P->name, current_cell->floor, current_cell->row, current_cell->col,
                   nxt_cell->floor, nxt_cell->row, nxt_cell->col);
        }
    }
    return nxt_cell;
}

int rand_int(int a, int b) {
    return a+(rand()%(abs(a-b)+1));
}

char* direction_name(Direction dir) {
    switch (dir) {
        case NORTH: return "NORTH";
        case EAST: return "EAST";
        case SOUTH: return "SOUTH";
        case WEST: return "WEST";
        default: return "UNKNOWN";
    }
}

void initialize_consumables(){
    int cell_count = 0;
    int normal_cells = 0;

    // Count normal cells
    for (int f = 0; f < FLOORS; f++) {
        for (int r = 0; r < ROWS; r++) {
            for (int c = 0; c < COLS; c++) {
                Cell *cell = get_cell(f, r, c);
                if (cell && check_cell(cell)){
                    normal_cells++;
                }
            }
        }
    }

    printf("Found %d normal cells\n", normal_cells);

    if (normal_cells == 0) {
        printf("No normal cells found, skipping consumables initialization\n");
        return;
    }

    int c_0_a = 0, b_12_a = 0, c_1_a = 0, b_35_a = 0, mul_23_a = 0;
    int c_0 = (int) (0.25 * normal_cells);
    int c_1 = (int) (0.35 * normal_cells);
    int b_12 = (int) (0.25 * normal_cells);
    int b_35 = (int) (0.10 * normal_cells);
    int mul_23 = (int) (0.05 * normal_cells);

    int attempts = 0;
    int max_attempts = normal_cells * 10; // Prevent infinite loop

    while (cell_count < normal_cells && attempts < max_attempts){
        attempts++;
        Cell *random_cell = get_cell(rand_int(0, 2), rand_int(0, 9), rand_int(0, 24));
        if (random_cell && random_cell->type == NORMAL && random_cell->cost == 0 && !random_cell->mul) {
            if (c_1_a < c_1){
                random_cell->cost = rand_int(1, 4);  // Positive cost (consumable)
                cell_count++;
                c_1_a++;
            }
            else if (b_12_a < b_12){
                random_cell->cost = -rand_int(1, 2);  // Negative cost (bonus)
                cell_count++;
                b_12_a++;
            }
            else if (b_35_a < b_35){
                random_cell->cost = -rand_int(3, 5);  // Negative cost (bonus)
                cell_count++;
                b_35_a++;
            }
            else if (mul_23_a < mul_23){
                random_cell->cost = rand_int(2, 3);
                random_cell->mul = true;
                cell_count++;
                mul_23_a++;
            }
            else if (c_0_a < c_0){
                random_cell->cost = 0;
                cell_count++;
                c_0_a++;
            }
            else {
                // If all categories are filled, break
                break;
            }
        }
    }
    printf("Initialized consumables for %d cells (attempts: %d)\n", cell_count, attempts);
}

void initialize_bawana() {
    int added = 0;

    // Initialize all bawana cells to POINTS first
    for (int r = 7; r <= 9; r++) {
        for (int c = 21; c <= 24; c++) {
            Cell *cell = get_cell(0, r, c);
            if (cell && cell->type == BAWANA) {
                cell->bw_type = POINTS;
                cell->bw_mp = rand_int(10, 100);
            }
        }
    }

    // Add 2 FOOD_P cells
    int food_p_added = 0;
    while (food_p_added < 2 && added < 12) {
        Cell *random = get_cell(0, rand_int(7, 9), rand_int(21, 24));
        if (random && random->type == BAWANA && random->bw_type == POINTS) {
            random->bw_type = FOOD_P;
            food_p_added++;
            added++;
        }
    }

    // Add 2 DISORIENTED cells
    int dis_added = 0;
    while (dis_added < 2 && added < 12) {
        Cell *random = get_cell(0, rand_int(7, 9), rand_int(21, 24));
        if (random && random->type == BAWANA && random->bw_type == POINTS) {
            random->bw_type = DISORIENTED;
            dis_added++;
            added++;
        }
    }

    // Add 2 TRIGGERED cells
    int trig_added = 0;
    while (trig_added < 2 && added < 12) {
        Cell *random = get_cell(0, rand_int(7, 9), rand_int(21, 24));
        if (random && random->type == BAWANA && random->bw_type == POINTS) {
            random->bw_type = TRIGGERED;
            trig_added++;
            added++;
        }
    }

    // Add 2 HAPPY cells
    int happy_added = 0;
    while (happy_added < 2 && added < 12) {
        Cell *random = get_cell(0, rand_int(7, 9), rand_int(21, 24));
        if (random && random->type == BAWANA && random->bw_type == POINTS) {
            random->bw_type = HAPPY;
            happy_added++;
            added++;
        }
    }

    printf("Initialized Bawana with %d special cells\n", added);
}

void bawana_effect(Player *p) {
    Cell *b_cell = get_cell(0,rand_int(5,9), rand_int(21,24));
    if (!b_cell) return;
    Bw_Type type = b_cell->bw_type;
    switch (type)
    {
    case FOOD_P:
        p->skips=3;
        printf("%c Eats from bawana. get food poisoning and 3 tuurns are set to be skipped\n", p->name);
        break;
    case TRIGGERED:
        p->triggered=true;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->mp += 50;
        p->direction=NORTH;
        printf("%c Eats from bawana. get triggered and exits from bawana\n", p->name);
        break;
    case DISORIENTED:
        p->dis_left=4;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->mp += 50;
        p->direction=NORTH;
        printf("%c Eats from bawana. get disoriented and 4 turns are set to be random directional\n", p->name);
        break;
    case POINTS:
        p->mp += b_cell->bw_mp;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->direction=NORTH;
        printf("%c Eats from bawana. get points and exits bawana\n", p->name);
        break;
    case HAPPY:
        p->mp += 200;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->direction=NORTH;
        printf("%c Eats from bawana. get happy get 200 mp and exits bawana\n", p->name);
        break;
    
    default:
        break;
    }
}


void apply_consumables(Player *p, Cell *cell){
    if (cell == NULL) return;

    if (cell->mul) {
        // Multiplier bonus
        p->mp *= cell->cost;
    } else if (cell->cost > 0) {
        // Consumable (costs MP)
        p->mp -= cell->cost;
    } else if (cell->cost < 0) {
        // Bonus (adds MP)
        p->mp += (-cell->cost);
    }

    // Cap MP at 250
    if (p->mp > 250) {
        p->mp = 250;
    }
}


void load_seed() {
    FILE *file = fopen("seed.txt", "r");
    unsigned int seed;
    if (!file || fscanf(file, "%u", &seed) != 1) {
        seed = (unsigned int)time(NULL);
        printf("Using random seed: %u\n", seed);
    } else {
        printf("Using seed from file: %u\n", seed);
    }
    if (file) fclose(file);
    srand(seed);
}

void load_flag() {
    FILE *file = fopen("flag.txt", "r");
    int floor, row, col;
    if (!file || fscanf(file, "[%d,%d,%d]", &floor, &row, &col) != 3) {
        // Place flag randomly
        do {
            floor = rand_int(0, 2);
            row = rand_int(0, 9);
            col = rand_int(0, 24);
        } while (!check_cell(get_cell(floor, row, col)));
        printf("Flag placed randomly at [%d,%d,%d]\n", floor, row, col);
    } else {
        printf("Flag loaded from file at [%d,%d,%d]\n", floor, row, col);
    }
    if (file) fclose(file);

    // Store flag position in global variables
    FLAG_FLOOR = floor;
    FLAG_ROW = row;
    FLAG_COL = col;
}

void initialize_players() {
    // Player A
    PLAYERS[0].name = 'A';
    PLAYERS[0].floor = 0;
    PLAYERS[0].row = 6;
    PLAYERS[0].col = 12;
    PLAYERS[0].init_row = 6;
    PLAYERS[0].init_col = 12;
    PLAYERS[0].s_row = 5;
    PLAYERS[0].s_col = 12;
    PLAYERS[0].direction = NORTH;
    PLAYERS[0].start_direction = NORTH;
    PLAYERS[0].in_board = 0;
    PLAYERS[0].left_to_dir_change = 0;
    PLAYERS[0].skips = 0;
    PLAYERS[0].triggered = false;
    PLAYERS[0].mp = 100;
    PLAYERS[0].dis_left = 0;

    // Player B
    PLAYERS[1].name = 'B';
    PLAYERS[1].floor = 0;
    PLAYERS[1].row = 9;
    PLAYERS[1].col = 8;
    PLAYERS[1].init_row = 9;
    PLAYERS[1].init_col = 8;
    PLAYERS[1].s_row = 9;
    PLAYERS[1].s_col = 7;
    PLAYERS[1].direction = WEST;
    PLAYERS[1].start_direction = WEST;
    PLAYERS[1].in_board = 0;
    PLAYERS[1].left_to_dir_change = 0;
    PLAYERS[1].skips = 0;
    PLAYERS[1].triggered = false;
    PLAYERS[1].mp = 100;
    PLAYERS[1].dis_left = 0;

    // Player C
    PLAYERS[2].name = 'C';
    PLAYERS[2].floor = 0;
    PLAYERS[2].row = 9;
    PLAYERS[2].col = 16;
    PLAYERS[2].init_row = 9;
    PLAYERS[2].init_col = 16;
    PLAYERS[2].s_row = 9;
    PLAYERS[2].s_col = 17;
    PLAYERS[2].direction = EAST;
    PLAYERS[2].start_direction = EAST;
    PLAYERS[2].in_board = 0;
    PLAYERS[2].left_to_dir_change = 0;
    PLAYERS[2].skips = 0;
    PLAYERS[2].triggered = false;
    PLAYERS[2].mp = 100;
    PLAYERS[2].dis_left = 0;
}

void initialize_game() {
    load_seed();
    init_floors();
    load_stairs();
    load_poles();
    load_walls();
    init_stairs();
    init_poles();
    init_walls();
    initialize_consumables();
    initialize_bawana();
    load_flag();
    initialize_players();
    printf("Game initialized successfully!\n");
}

int roll_dice() {
    return rand_int(1, 6);
}

int roll_direction_dice() {
    int face = rand_int(1, 6);
    switch(face) {
        case 2: return NORTH;
        case 3: return EAST;
        case 4: return SOUTH;
        case 5: return WEST;
        default: return -1; // Empty face
    }
}

int check_win(Player *p) {
    return (p->in_board && p->floor == FLAG_FLOOR && p->row == FLAG_ROW && p->col == FLAG_COL);
}

void handle_stairs_direction(int round) {
    if (round % STAIRS_CHANGE_EVERY_ROUNDS == 0 && round != 0) {
        randomize_stairs_direction();
        printf(" Stairs direction changed\n");
        for (int i = 0; i < stairs_num; i++) {
            printf("  Stair%d at [%d,%d,%d,%d,%d,%d] to %s\n", i+1,
                   STAIRS[i].s_floor, STAIRS[i].s_row, STAIRS[i].s_col,
                   STAIRS[i].e_floor, STAIRS[i].e_row, STAIRS[i].e_col,
                   (STAIRS[i].dir == UP) ? "up" : (STAIRS[i].dir == DOWN) ? "down" : "Bi-Directional");
        }
        printf("\n");
    }
}

void randomize_stairs_direction() {
    for (int i = 0; i < stairs_num; i++) {
        int random_012 = rand_int(0, 2); // 0=BIDIRECTION, 1=UP, 2=DOWN
        STAIRS[i].dir = random_012;
    }
}

void play_game() {
    initialize_game();

    printf("\n=== MAZE RUNNER GAME STARTED ===\n");
    printf("Flag is at [%d,%d,%d]\n\n", FLAG_FLOOR, FLAG_ROW, FLAG_COL);

    int round = 0;
    int game_over = 0;

    while (!game_over && round < 1000000) {
        round++;
        printf("\n--- Round %d ---\n", round);

        handle_stairs_direction(round);

        for (int i = 0; i < 3; i++) {
            Player *p = &PLAYERS[i];

            // Skip if player has food poisoning
            if (p->skips > 0) {
                p->skips--;
                printf("%c is still food poisoned and misses the turn.\n", p->name);
                if (p->skips == 0) {
                    printf("%c is now fit to proceed from the food poisoning episode.\n", p->name);
                    bawana_effect(p);
                }
                continue;
            }

            int move_die = roll_dice();
            printf("%c rolls %d on movement dice. ", p->name, move_die);

            // Check if player needs to enter maze
            if (!p->in_board) {
                if (move_die == 6) {
                    p->in_board = 1;
                    p->floor = 0;
                    p->row = p->s_row;
                    p->col = p->s_col;
                    p->direction = p->start_direction;
                    printf("%c enters the maze at [%d,%d,%d]\n", p->name, p->floor, p->row, p->col);
                } else {
                    printf("%c cannot enter the maze.\n", p->name);
                    continue;
                }
            } else {
                // Player is in maze, attempt movement
                printf("Current position: [%d,%d,%d], Direction: %s\n",
                       p->floor, p->row, p->col, direction_name(p->direction));

                // Check for direction change every 4th throw
                p->left_to_dir_change++;
                if (p->left_to_dir_change >= 4) {
                    p->left_to_dir_change = 0;
                    int dir_die = roll_direction_dice();
                    if (dir_die != -1) {
                        p->direction = (Direction)dir_die;
                        printf("Direction changed to %s. ", direction_name(p->direction));
                    }
                }

                // Attempt movement
                int steps = move_die;
                if (p->triggered) {
                    steps *= 2;
                    printf("Triggered - moving %d steps. ", steps);
                }

                attempt_move(p, steps);

                // Check win condition
                if (check_win(p)) {
                    printf("\n*** PLAYER %c WINS! ***\n", p->name);
                    printf("Game ended in round %d\n", round);
                    game_over = 1;
                    break;
                }

                // Debug: Check if player is close to flag
                if (p->floor == FLAG_FLOOR && abs(p->row - FLAG_ROW) <= 2 && abs(p->col - FLAG_COL) <= 2) {
                    printf("*** %c is close to flag! Position: [%d,%d,%d], Flag: [%d,%d,%d] ***\n",
                           p->name, p->floor, p->row, p->col, FLAG_FLOOR, FLAG_ROW, FLAG_COL);
                }
            }
        }
    }

    if (!game_over) {
        printf("\nGame ended after %d rounds with no winner.\n", round);
    }
}

int main() {
    play_game();
    return 0;
}

